{"name": "astro-micro", "type": "module", "version": "0.0.2", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.2.3", "@astrojs/rss": "^4.0.11", "@astrojs/sitemap": "^3.3.0", "@fontsource/geist-mono": "^5.2.5", "@fontsource/geist-sans": "^5.2.5", "@fontsource/ibm-plex-mono": "^5.2.5", "@fontsource/recursive": "^5.2.5", "@radix-ui/react-icons": "^1.3.2", "@tailwindcss/vite": "^4.1.3", "astro": "^5.6.1", "astro-mermaid": "^1.0.4", "astro-pagefind": "^1.8.3", "clsx": "^2.1.1", "mermaid": "^11.9.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "typescript": "^5.8.3", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "pagefind": "^1.3.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.11"}}