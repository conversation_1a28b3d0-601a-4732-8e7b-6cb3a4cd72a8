---
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import ArrowCard from "@components/ArrowCard.astro";
import BackToPrevious from "@components/BackToPrevious.astro";

export async function getStaticPaths() {
  const posts = await getCollection("blog", ({ data }) => !data.draft);

  // Get unique tags
  const tags = [...new Set(posts.flatMap((post) => post.data.tags || []))];

  // Create paths for each tag
  return tags.map((tag) => ({
    params: { id: tag },
    props: {
      posts: posts.filter((post) => post.data.tags?.includes(tag)),
    },
  }));
}

const { id } = Astro.params;
const { posts } = Astro.props;

// Sort posts by date, most recent first
const sortedPosts = posts.sort(
  (a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime(),
);
---

<Layout title={`Tag: ${id}`} description={`Posts with the tag: ${id}`}>
  <Container>
    <div class="space-y-10" data-pagefind-ignore>
      <BackToPrevious href="/tags"> All tags </BackToPrevious>
      <h1 class="animate font-semibold text-black dark:text-white">
        Posts tagged with "{id}"
      </h1>
      <ul class="animate flex flex-col gap-4">
        {sortedPosts.map((post) => <ArrowCard entry={post} />)}
      </ul>
    </div>
  </Container>
</Layout>
