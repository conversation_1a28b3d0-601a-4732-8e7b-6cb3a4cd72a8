---
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";

const posts = await getCollection("blog", ({ data }) => !data.draft);
const tags = [...new Set(posts.flatMap((post) => post.data.tags || []))].sort();
---

<Layout title="Tags" description="List of tags used.">
  <Container>
    <div class="space-y-10">
      <h1 class="animate font-semibold">All Tags</h1>
      <div class="animate flex flex-wrap gap-2">
        {
          tags.map((tag) => (
            <a
              href={`/tags/${tag}`}
              class="rounded-sm border border-black/15 px-2 py-1 text-sm transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
            >
              {tag}{" "}
              <span class="text-sm text-gray-600">
                ({posts.filter((post) => post.data.tags?.includes(tag)).length})
              </span>
            </a>
          ))
        }
      </div>
    </div>
  </Container>
</Layout>
