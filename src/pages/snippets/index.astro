---
import { type CollectionEntry, getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import ArrowCard from "@components/ArrowCard.astro";
import { SNIPPETS } from "@consts";


const data = (await getCollection("snippets"))
  .filter((post) => !post.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());

type Acc = {
  [year: string]: CollectionEntry<"snippets">[];
};

const posts = data.reduce((acc: Acc, post) => {
  const year = post.data.date.getFullYear().toString();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push(post);
  return acc;
}, {});

const years = Object.keys(posts).sort((a, b) => parseInt(b) - parseInt(a));
---

<Layout title={SNIPPETS.TITLE} description={SNIPPETS.DESCRIPTION}>
  <Container>
    <aside data-pagefind-ignore>
      <div class="space-y-10">
        <div class="space-y-4">
          {
            years.map((year) => (
              <section class="animate space-y-4">
                <div class="font-semibold text-black dark:text-white">
                  {year}
                </div>
                <div>
                  <ul class="not-prose flex flex-col gap-4">
                    {posts[year].map((post) => (
                      <li>
                        <ArrowCard entry={post} />
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            ))
          }
        </div>
      </div>
    </aside>
  </Container>
</Layout>
