---
import { type CollectionEntry, getCollection, render } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import FormattedDate from "@components/FormattedDate.astro";
import { readingTime } from "@lib/utils";
import BackToPrevious from "@components/BackToPrevious.astro";
import PostNavigation from "@components/PostNavigation.astro";
import TableOfContents from "@components/TableOfContents.astro";
// import Giscus from "@components/Giscus.astro";

export async function getStaticPaths() {
  const notes = (await getCollection("notes"))
    .filter((note) => !note.data.draft)
    .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());
  return notes.map((post) => ({
    params: { id: post.id },
    props: post,
  }));
}
type Props = CollectionEntry<"notes">;

const notes = (await getCollection("notes"))
  .filter((post) => !post.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());

function getNextNote() {
  let noteIndex;
  for (const note of notes) {
    if (note.id === Astro.params.id) {
      noteIndex = notes.indexOf(note);
      return notes[noteIndex + 1];
    }
  }
}

function getPrevNote() {
  let noteIndex;
  for (const note of notes) {
    if (note.id === Astro.params.id) {
      noteIndex = notes.indexOf(note);
      return notes[noteIndex - 1];
    }
  }
}

const nextNote = getNextNote();
const prevNote = getPrevNote();

const note = Astro.props;
const { Content, headings } = await render(note);
---

<Layout title={note.data.title} description={note.data.description}>
  <Container>
    <div class="animate">
      <BackToPrevious href="/notes">Back to notes</BackToPrevious>
    </div>
    <div class="my-10 space-y-1">
      <div class="animate flex items-center gap-1.5">
        <div class="font-base text-sm">
          <FormattedDate date={note.data.date} />
        </div>
        &bull;
        {note.body && (
          <div class="font-base text-sm">
            {readingTime(note.body)}
          </div>
        )}        
      </div>
      <h1 class="animate text-3xl font-semibold text-black dark:text-white">
        {note.data.title}
      </h1>
      {
        note.data.tags && note.data.tags?.length > 0 ? (
          <div class="animate flex gap-2 pt-1">
            {note.data.tags.map((tag) => (
              <a
                href={`/tags/${tag}`}
                class="rounded-sm border border-black/15 px-2 py-1 text-sm transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
              >
                {tag}
              </a>
            ))}
          </div>
        ) : null
      }
    </div>
    {headings.length > 0 && <TableOfContents headings={headings} />}
    <article class="animate">
      <Content />
      <div class="mt-24">
        <PostNavigation prevPost={prevNote} nextPost={nextNote} path={"notes"} />
      </div>
      <div class="mt-24">
        {/* <Giscus /> */}
      </div>
    </article>
  </Container>
</Layout>
