---
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import { SITE, HOME, SOCIALS } from "@consts";
import ArrowCard from "@components/ArrowCard.astro";
import Link from "@components/Link.astro";
import { getCollection } from "astro:content";
import type { CollectionEntry } from "astro:content";
import Callout from "@components/Callout.astro";

const blog = (await getCollection("blog"))
  .filter((post) => !post.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf())
  .slice(0, SITE.NUM_POSTS_ON_HOMEPAGE);

const projects: CollectionEntry<"projects">[] = (
  await getCollection("projects")
)
  .filter((project) => !project.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf())
  .slice(0, SITE.NUM_PROJECTS_ON_HOMEPAGE);
---

<Layout title={HOME.TITLE} description={HOME.DESCRIPTION}>
  <Container>
    <aside data-pagefind-ignore>
      <!-- Hero Section with progressive load -->
      <div class="progressive-load space-y-8" style="--load-order: 1">
        <div class="space-y-4">
          <Callout type="hi">
            <h1 class="font-semibold text-black dark:text-white">
              Hi, I am <b>Barun Debnath</b>
            </h1>
          </Callout>
          <p>
            🚀 Exploring the world of <b>SRE</b>, <b>Systems</b>, and <b
              >Platform Engineering</b
            >, one system crash (oops, <b>incident</b>) at a time.
          </p>
          <p>
            When I'm not debugging or building, you'll find me buried in mangas,
            sipping coffee, and obsessing over <b>that one perfect setup</b>.
          </p>
        </div>
        <div class="flex w-full justify-center">
          <img
            src="https://assets.barundebnath.com/about-me%20(1).png"
            alt="Ninja coding on laptop - Barun's digital avatar"
            class="h-auto w-full max-w-[800px] rounded-lg"
            width="800"
            height="800"
            loading="lazy"
            fetchpriority="low"
            decoding="async"
            sizes="(max-width: 800px) 100vw, 800px"
          />
        </div>
      </div>

      <div class="space-y-16">
        <!-- Work Experience Section -->
        <section class="progressive-load" style="--load-order: 2">
          <article class="space-y-4">
            <p>
              <Callout type="work">
                <b>What I've Been Up To</b>
              </Callout>
              <b>Here's my highlight reel so far:</b>
            </p>
            <div class="animate -mt-10">
              <div class="overflow-x-auto">
                <table class="w-full border-collapse">
                  <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                      <th class="py- px-4 text-left">Designation</th>
                      <th class="px-4 py-2 text-left">Organization</th>
                      <th class="px-4 py-2 text-left">Duration</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                      <td class="px-4 py-2">SRE</td>
                      <td class="px-4 py-2">
                        <Link href="https://one2n.io/" external>One2N</Link>
                      </td>
                      <td class="px-4 py-2">Jun'24 - Present</td>
                    </tr>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                      <td class="px-4 py-2">SRE</td>
                      <td class="px-4 py-2">
                        <Link href="https://www.media.net/" external
                          >Media.Net</Link
                        >
                      </td>
                      <td class="px-4 py-2">Jul'23 - Jun'24</td>
                    </tr>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                      <td class="px-4 py-2">SRE Intern</td>
                      <td class="px-4 py-2">
                        <Link href="https://www.media.net/" external
                          >Media.Net</Link
                        >
                      </td>
                      <td class="px-4 py-2">Jan'23 - Jun'23</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <p class="mt-4 flex flex-col gap-2">
              <span
                >From firefighting production issues to building scalable
                platforms, the journey's been a wild ride!</span
              >
              <span>
                Know more about my work experience from my <Link
                  href="https://drive.google.com/file/d/1kEe29ZLiOqxdEV0eDXisdqvwei3Fn4zh/view?usp=sharing"
                  external>RESUME</Link
                >
              </span>
            </p>
          </article>
        </section>

        <section class="animate space-y-6">
          <div class="flex flex-wrap items-center justify-between gap-y-2">
            <h2 class="font-semibold text-black dark:text-white">
              Latest posts
            </h2>
            <Link href="/blog"> See all posts </Link>
          </div>
          <ul class="not-prose flex flex-col gap-4">
            {
              blog.map((post) => (
                <li>
                  <ArrowCard entry={post} />
                </li>
              ))
            }
          </ul>
        </section>

        <section class="animate space-y-6">
          <div class="flex flex-wrap items-center justify-between gap-y-2">
            <h2 class="font-semibold text-black dark:text-white">
              Recent projects
            </h2>
            <Link href="/projects"> See all projects </Link>
          </div>
          <ul class="not-prose flex flex-col gap-4">
            {
              projects.map((project) => (
                <li>
                  <ArrowCard entry={project} />
                </li>
              ))
            }
          </ul>
        </section>
      </div>

      <section class="progressive-load space-y-4" style="--load-order: 5">
        <div class="space-y-4">
          <Callout type="chat">
            <h2 class="font-semibold text-black dark:text-white">
              Want to Chat?
            </h2>
          </Callout>
          <article class="space-y-2">
            <p>
              Whether it's about DevOps musings, system design discussions, or
              just sharing memes - I'm always up for a good conversation!
            </p>
            <p>
              Grab a coffee, poke around, and don't forget to say hi! You can
              find me at:
            </p>
          </article>
          <ul class="not-prose flex flex-wrap gap-2">
            {
              SOCIALS.map((SOCIAL) => (
                <li class="flex gap-x-2 text-nowrap">
                  <Link
                    href={SOCIAL.HREF}
                    external
                    aria-label={`${SITE.TITLE} on ${SOCIAL.NAME}`}
                  >
                    {SOCIAL.NAME}
                  </Link>
                  {"/"}
                </li>
              ))
            }
            <li class="line-clamp-1">
              <Link
                href={`mailto:${SITE.EMAIL}`}
                aria-label={`Email ${SITE.TITLE}`}
              >
                {SITE.EMAIL}
              </Link>
            </li>
          </ul>
        </div>
      </section>
    </aside>
  </Container>
</Layout>
