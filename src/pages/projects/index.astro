---
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import ArrowCard from "@components/ArrowCard.astro";
import { PROJECTS } from "@consts";

const projects = (await getCollection("projects"))
  .filter((project) => !project.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());
---

<Layout title={PROJECTS.TITLE} description={PROJECTS.DESCRIPTION}>
  <Container>
    <aside data-pagefind-ignore>
      <div class="space-y-10">
        <div class="animate font-semibold text-black dark:text-white">
          Projects
        </div>
        <ul class="animate not-prose flex flex-col gap-4">
          {
            projects.map((project) => (
              <li>
                <ArrowCard entry={project} />
              </li>
            ))
          }
        </ul>
      </div>
    </aside>
  </Container>
</Layout>
