---
import { type CollectionEntry, getCollection, render } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import FormattedDate from "@components/FormattedDate.astro";
import { readingTime } from "@lib/utils";
import BackToPrevious from "@components/BackToPrevious.astro";
import Link from "@components/Link.astro";
import TableOfContents from "@components/TableOfContents.astro";

export async function getStaticPaths() {
  const projects = (await getCollection("projects"))
    .filter((post) => !post.data.draft)
    .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());
  return projects.map((project) => ({
    params: { id: project.id },
    props: project,
  }));
}
type Props = CollectionEntry<"projects">;

const project = Astro.props;
const { Content, headings } = await render(project);
---

<Layout title={project.data.title} description={project.data.description}>
  <Container>
    <div class="animate">
      <BackToPrevious href="/projects">Back to projects</BackToPrevious>
    </div>
    <div class="animate my-10 space-y-1">
      <div class="flex items-center gap-1.5">
        <div class="font-base text-sm">
          <FormattedDate date={project.data.date} />
        </div>
        &bull;
        {project.body && (
          <div class="font-base text-sm">
            {readingTime(project.body)}
          </div>
        )}     
      </div>
      <h1 class="text-3xl font-semibold text-black dark:text-white">
        {project.data.title}
      </h1>
      {
        (project.data.demoURL || project.data.repoURL) && (
          <nav class="flex gap-1">
            {project.data.demoURL && (
              <Link href={project.data.demoURL} external>
                demo
              </Link>
            )}
            {project.data.demoURL && project.data.repoURL && <span>/</span>}
            {project.data.repoURL && (
              <Link href={project.data.repoURL} external>
                repo
              </Link>
            )}
          </nav>
        )
      }
    </div>
    <TableOfContents headings={headings} />
    <article class="animate">
      <Content />
    </article>
  </Container>
</Layout>
