---
import { getCollection } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import ArrowCard from "@components/ArrowCard.astro";
import { BLOG } from "@consts";

// Get all blog posts
const allBlogPosts = await getCollection("blog", ({ data }) => !data.draft);

// Sort all posts by date (newest first)
const sortedPosts = allBlogPosts
  .map(post => ({
    ...post,
    data: {
      ...post.data,
      // Add any additional processing here if needed
    }
  }))
  .sort((a, b) => b.data.date.getTime() - a.data.date.getTime());

// Group by year
const postsByYear = sortedPosts.reduce((acc: { [key: string]: typeof sortedPosts }, post) => {
  const year = post.data.date.getFullYear().toString();
  if (!acc[year]) {
    acc[year] = [];
  }
  acc[year].push({
    ...post,
    // Add any additional properties needed by ArrowCard
  });
  return acc;
}, {});

const years = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));
---

<Layout title={BLOG.TITLE} description={BLOG.DESCRIPTION}>
  <Container>
    <aside data-pagefind-ignore>
      <div class="space-y-10">
        <div class="space-y-4">
          {
            years.map((year) => (
              <section class="animate space-y-4 mt-8">
                <div class="font-semibold text-black dark:text-white">
                  {year}
                </div>
                <div>
                  <ul class="not-prose flex flex-col gap-4">
                    {postsByYear[year].map((post) => (
                      <li>
                        <ArrowCard entry={{
                          id: post.id,
                          slug: post.slug,
                          collection: post.collection,
                          data: {
                            ...post.data,
                            // Ensure all required fields are present
                            body: post.body,
                            // Handle redirected posts
                            redirected: post.data.redirected || false,
                            redirectedUrl: post.data.redirectedUrl
                          }
                        }} />
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            ))
          }
        </div>
      </div>
    </aside>
  </Container>
</Layout>
