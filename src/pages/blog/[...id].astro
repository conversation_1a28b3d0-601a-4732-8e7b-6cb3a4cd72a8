---
import { type CollectionEntry, getCollection, render } from "astro:content";
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import FormattedDate from "@components/FormattedDate.astro";
import { readingTime } from "@lib/utils";
import BackToPrevious from "@components/BackToPrevious.astro";
import PostNavigation from "@components/PostNavigation.astro";
import TableOfContents from "@components/TableOfContents.astro";
// import Giscus from "@components/Giscus.astro";

export async function getStaticPaths() {
  const posts = (await getCollection("blog"))
    .filter((post) => !post.data.draft)
    .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());
  return posts.map((post) => ({
    params: { id: post.id.replace(/\/index\.mdx?$/, '') },
    props: post,
  }));
}
type Props = CollectionEntry<"blog">;

const posts = (await getCollection("blog"))
  .filter((post) => !post.data.draft)
  .sort((a, b) => b.data.date.valueOf() - a.data.date.valueOf());

function getNextPost() {
  let postIndex;
  for (const post of posts) {
    if (post.id.replace(/\/index\.mdx?$/, '') === Astro.params.id) {
      postIndex = posts.indexOf(post);
      return posts[postIndex + 1];
    }
  }
}

function getPrevPost() {
  let postIndex;
  for (const post of posts) {
    if (post.id.replace(/\/index\.mdx?$/, '') === Astro.params.id) {
      postIndex = posts.indexOf(post);
      return posts[postIndex - 1];
    }
  }
}

const nextPost = getNextPost();
const prevPost = getPrevPost();

const post = Astro.props;
const { Content, headings } = await render(post);
---

<Layout title={post.data.title} description={post.data.description}>
  <Container>
    <div class="animate">
      <BackToPrevious href="/blog">Back to blog</BackToPrevious>
    </div>
    <div class="my-10 space-y-1">
      <div class="animate flex items-center gap-1.5">
        <div class="font-base text-sm">
          <FormattedDate date={post.data.date} />
        </div>
        &bull;
        {post.body && (
          <div class="font-base text-sm">
            {readingTime(post.body)}
          </div>
        )}        
      </div>
      <h1 class="animate text-3xl font-semibold text-black dark:text-white">
        {post.data.title}
      </h1>
      {
        post.data.tags && post.data.tags?.length > 0 ? (
          <div class="animate flex gap-2 pt-1">
            {post.data.tags.map((tag) => (
              <a
                href={`/tags/${tag}`}
                class="rounded-sm border border-black/15 px-2 py-1 text-sm transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
              >
                {tag}
              </a>
            ))}
          </div>
        ) : null
      }
    </div>
    {headings.length > 0 && <TableOfContents headings={headings} />}
    <article class="animate">
      <Content />
      <div class="mt-24">
        <PostNavigation prevPost={prevPost} nextPost={nextPost} path={"blog"}/>
      </div>
      <div class="mt-24">
        {/* <Giscus /> */}
      </div>
    </article>
  </Container>
</Layout>
