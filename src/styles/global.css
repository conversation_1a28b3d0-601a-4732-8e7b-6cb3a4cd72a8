@import "tailwindcss";

@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans:
    Recursive, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono:
    "IBM Plex Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
}

@layer base {
  :root {
    /* Green-based color palette */
    --color-primary: #0e9b77; /* Secondary */
    --color-secondary: #074d3b; /* Dark */
    --color-accent: #2fb492; /* Lightgray */
    --color-background: #f9f4ee; /* Light */
    --color-text: #5a534d; /* Darkgray */
    --color-muted: #c0b8b0; /* Gray */
    --color-highlight: #c4d7c472; /* Highlight */
    --color-text-highlight: #fff23688; /* Text highlight */
    --color-searchbar: #ebe6e0; /* Searchbar */
    --color-tertiary: #0a674f; /* Tertiary */

    /* Theme transition properties - exclude color for instant text changes */
    --transition-theme:
      background-color 0.3s ease, border-color 0.3s ease, fill 0.3s ease,
      stroke 0.3s ease, opacity 0.3s ease, box-shadow 0.3s ease,
      transform 0.3s ease, filter 0.3s ease, backdrop-filter 0.3s ease,
      -webkit-box-shadow 0.3s ease, -webkit-filter 0.3s ease,
      -webkit-backdrop-filter 0.3s ease;
  }

  .dark {
    --color-primary: #ff8da1; /* Softer sakura pink */
    --color-secondary: #7ad7f0; /* Softer ocean blue */
    --color-accent: #ffe55c; /* Softer gold */
    --color-background: #1e1e2e; /* Softer dark navy */
    --color-text: #cdd6f4; /* Softer light text */
    --color-muted: #a6adc8; /* Softer muted text */
  }
}

@layer utilities {
  html {
    overflow-y: auto;
    color-scheme: light;
    scroll-padding-top: 100px;
    --progressive-delay: 0.1s;
  }

  /* Inline code styling */
  :not(pre) > code {
    font-family: var(--font-mono);
    background-color: var(--color-highlight, #f3f4f6);
    color: var(--color-primary, #0e9b77);
    padding: 0.2em 0.4em;
    border-radius: 0.25em;
    font-size: 0.875em;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: normal;
    word-break: break-word;
    position: relative;
  }

  /* Hide backticks in inline code */
  :not(pre) > code::before,
  :not(pre) > code::after {
    content: "";
    display: none;
  }

  .dark :not(pre) > code {
    background-color: rgba(79, 70, 229, 0.15);
    color: var(--color-secondary, #7ad7f0);
  }

  /* Link animations */
  a {
    @apply relative inline-block transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)];
    text-decoration: none;
  }

  /* Underline animation */
  a:not(.no-underline)::after {
    content: "";
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--color-primary);
    transition:
      width 0.3s ease,
      background-color 0.3s ease;
  }

  a:not(.no-underline):hover::after {
    width: 100%;
  }

  a:active {
    transform: scale(0.97);
  }

  /* Icon animations */
  svg {
    @apply transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)];
  }

  button:hover svg,
  a:hover svg {
    transform: scale(1.1);
  }

  button:active svg,
  a:active svg {
    transform: scale(0.95);
  }

  /* Specific animation for theme toggle */
  .theme-toggle:hover svg {
    transform: rotate(15deg) scale(1.1);
  }

  .theme-toggle:active svg {
    transform: rotate(0) scale(0.95);
  }

  /* Progressive loading animation */
  .progressive-load {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
    animation-delay: calc(var(--progressive-delay) * var(--load-order, 1));
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  html.dark {
    color-scheme: dark;
  }

  html,
  body {
    @apply size-full;
  }

  body {
    @apply font-sans antialiased;
    @apply flex flex-col;
    @apply bg-[var(--color-background)];
    @apply text-[var(--color-text)];
    /* Smooth transitions except for text color */
    transition: var(--transition-theme);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Make text color change instantly */
    transition-property:
      background-color,
      border-color,
      fill,
      stroke,
      opacity,
      box-shadow,
      transform,
      filter,
      backdrop-filter,
      -webkit-box-shadow,
      -webkit-filter,
      -webkit-backdrop-filter;
    /* Force text color to change instantly */
    color: var(--color-text) !important;
  }

  header {
    @apply fixed top-0 right-0 left-0 z-50 py-6;
    @apply bg-[var(--color-background)]/75;
    @apply saturate-200 backdrop-blur-xs;
  }

  main {
    @apply flex-1 py-20;
  }

  footer {
    @apply py-6 text-sm;
  }

  article {
    @apply prose prose-neutral dark:prose-invert prose-img:mx-auto prose-img:my-auto max-w-full;
    @apply prose-headings:font-semibold;
    @apply prose-headings:text-[var(--color-secondary)];
    /* Smooth transitions except for text color */
    transition: var(--transition-theme);
    transition-property:
      background-color,
      border-color,
      fill,
      stroke,
      opacity,
      box-shadow,
      transform,
      filter,
      backdrop-filter,
      -webkit-box-shadow,
      -webkit-filter,
      -webkit-backdrop-filter;
    /* Force text color to change instantly */
    color: var(--color-text) !important;
  }

  .callout {
    @apply border-l-4 border-[var(--color-primary)] bg-[var(--color-highlight)] p-4;
    @apply rounded-lg shadow-sm;
  }

  .callout.hi {
    @apply border-[var(--color-primary)] bg-[var(--color-highlight)];
  }

  .callout.work {
    @apply border-[var(--color-secondary)] bg-[var(--color-highlight)];
  }

  .callout.chat {
    @apply border-[var(--color-accent)] bg-[var(--color-highlight)];
  }
}

@layer utilities {
  article a {
    @apply font-sans text-[var(--color-primary)] underline underline-offset-[3px];
    @apply decoration-[var(--color-primary)]/30;
    @apply transition-colors duration-300 ease-in-out;
    /* Make text color change instantly */
    transition-property:
      background-color,
      border-color,
      fill,
      stroke,
      opacity,
      box-shadow,
      transform,
      filter,
      backdrop-filter,
      -webkit-box-shadow,
      -webkit-filter,
      -webkit-backdrop-filter;
  }
  article a:hover {
    @apply text-[var(--color-secondary)];
    @apply decoration-[var(--color-secondary)]/50;
  }
}

.animate {
  @apply -translate-y-3 opacity-0;
  @apply transition-all duration-300 ease-out;
  /* Ensure smooth theme transitions - exclude text color */
  transition-property:
    transform,
    opacity,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    box-shadow,
    filter,
    backdrop-filter,
    -webkit-box-shadow,
    -webkit-filter,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.3s;
  /* Force text color to change instantly */
  color: var(--color-text) !important;
}

.animate.show {
  @apply translate-y-0 opacity-100;
}

html #back-to-top {
  @apply pointer-events-none opacity-0;
}

html.scrolled #back-to-top {
  @apply pointer-events-auto opacity-100;
}

/* shiki config */
pre {
  @apply border border-[var(--color-primary)]/15 py-5;
  /* Code block wrapping for all screen sizes */
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow-x: visible; /* No horizontal scrollbar */
  position: relative;
  padding-right: 3rem; /* Space for copy button */
}

pre code {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: block;
  padding-right: 0; /* Reset any default padding */
}

/* Remove previous attempt */

:root {
  --astro-code-foreground: #000000;
  --astro-code-background: #fafafa;
  --astro-code-token-comment: #686060;
  --astro-code-token-keyword: #c92f25;
  --astro-code-token-string: #007d70;
  --astro-code-token-function: #2d6967;
  --astro-code-token-constant: #356fa5;
  --astro-code-token-parameter: #0e5eb8;
  --astro-code-token-string-expression: #8c467f;
  --astro-code-token-punctuation: #3f4851;
  --astro-code-token-link: #4e3dfd;
}

.dark {
  --astro-code-foreground: #fafafa;
  --astro-code-background: #09090b;
  --astro-code-token-comment: #a19595;
  --astro-code-token-keyword: #f47067;
  --astro-code-token-string: #00a99a;
  --astro-code-token-function: #6eafad;
  --astro-code-token-constant: #b3cceb;
  --astro-code-token-parameter: #4e8fdf;
  --astro-code-token-string-expression: #bf7db6;
  --astro-code-token-punctuation: #8996a3;
  --astro-code-token-link: #8d85ff;
}

/* copy code button on codeblocks */
.copy-code {
  @apply absolute top-3 right-3 grid size-9 place-content-center rounded-sm border border-[var(--color-primary)]/15 bg-[var(--color-background)] text-center duration-300 ease-in-out;
}

.copy-code:hover {
  @apply bg-[var(--color-highlight)] transition-colors;
}

.copy-code:active {
  @apply scale-90 transition-transform;
}

/* Diagram styling */
.mermaid {
  @apply my-8 flex justify-center;
  @apply transition-all duration-300 ease-in-out;
}

.mermaid svg {
  @apply h-auto max-w-full;
  @apply rounded-lg;
}

.d2-diagram {
  @apply my-8 flex justify-center;
  @apply transition-all duration-300 ease-in-out;
}

.d2-diagram svg {
  @apply h-auto max-w-full;
  @apply rounded-lg shadow-sm;
  @apply border border-[var(--color-primary)]/10;
}

.d2-error {
  @apply my-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800;
  @apply dark:border-red-800/30 dark:bg-red-900/20 dark:text-red-200;
}

.d2-error details {
  @apply mt-2;
}

.d2-error summary {
  @apply cursor-pointer font-medium hover:text-red-600 dark:hover:text-red-300;
}

.d2-error pre {
  @apply mt-2 rounded bg-red-100 p-2 text-sm dark:bg-red-900/30;
  @apply border-none;
}

/* Dark mode adjustments for D2 diagrams */
.dark .d2-diagram svg {
  @apply brightness-90 contrast-110 filter;
}

/* Responsive diagram containers */
@media (max-width: 768px) {
  .mermaid,
  .d2-diagram {
    @apply mx-4;
  }
}

/* Additional responsive improvements for very small screens */
@media (max-width: 480px) {
  pre {
    font-size: 0.875rem; /* Slightly smaller font on mobile */
    padding: 1rem;
    padding-right: 2.5rem; /* Adjusted space for smaller copy button */
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .copy-code {
    @apply top-2 right-2 size-8;
    font-size: 0.75rem;
  }
}
