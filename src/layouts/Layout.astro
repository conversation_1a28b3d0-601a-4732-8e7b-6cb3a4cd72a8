---
import Head from "@components/Head.astro";
import Header from "@components/Header.astro";
import Footer from "@components/Footer.astro";
import Pagefind from "@components/PageFind.astro";
import { SITE } from "@consts";

type Props = { title: string; description: string };

const { title, description } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <link rel="sitemap" href="/sitemap.xml" />
    <Head title={`${title} | ${SITE.TITLE}`} description={description} />
    <script is:inline define:vars={{ isSSR: import.meta.env.SSR }}>
      // This script runs before the page renders to prevent FOUC
      (function() {
        // Only run on client-side
        if (typeof document === 'undefined') return;

        // Check for saved theme preference or use system preference
        const getTheme = () => {
          try {
            if (typeof localStorage !== 'undefined' && localStorage.theme) {
              return localStorage.theme;
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          } catch (e) {
            return 'light'; // Default to light if there's an error
          }
        };

        // Apply theme immediately to prevent flash
        const theme = getTheme();
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
          document.documentElement.style.colorScheme = 'dark';
        } else {
          document.documentElement.classList.remove('dark');
          document.documentElement.style.colorScheme = 'light';
        }

        // Store the theme in a data attribute for CSS to use
        document.documentElement.setAttribute('data-theme', theme);
      })();
    </script>
    <!-- Progressive loading script -->
    <script src="/utils/progressiveLoad.js" type="module" defer></script>
    <style>
      /* Prevent flash of light theme */
      :root:not([data-theme]) {
        visibility: hidden;
      }
      :root[data-theme] {
        visibility: visible;
      }
    </style>

    <script is:inline>
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split(".");
              2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                });
            }
            ((p = t.createElement("script")).type = "text/javascript"),
              (p.crossOrigin = "anonymous"),
              (p.async = !0),
              (p.src =
                s.api_host.replace(".i.posthog.com", "-assets.i.posthog.com") +
                "/static/array.js"),
              (r = t.getElementsByTagName("script")[0]).parentNode.insertBefore(
                p,
                r,
              );
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = "posthog"),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = "posthog";
                  return (
                    "posthog" !== a && (e += "." + a), t || (e += " (stub)"), e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + ".people (stub)";
                },
                o =
                  "init bs ws ge fs capture De Ai $s register register_once register_for_session unregister unregister_for_session Is getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty xs Ss createPersonProfile Es gs opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing ys debug ks getPageViewId captureTraceFeedback captureTraceMetric".split(
                    " ",
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);
      posthog.init("phc_DkyC50rMDLqlPtrbY2ZTBrU6CjWFAtXezQ8KcyykHTy", {
        api_host: "https://us.i.posthog.com",
        person_profiles: "identified_only", // or 'always' to create profiles for anonymous users as well
      });
    </script>
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NWW6TLLD"
        height="0"
        width="0"
        style="display:none;visibility:hidden"></iframe></noscript
    >
    <!-- End Google Tag Manager (noscript) -->
    <Header />
    <main>
      <slot />
    </main>
    <Footer />
    <Pagefind />
  </body>
</html>
