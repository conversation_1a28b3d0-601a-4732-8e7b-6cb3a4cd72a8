<button
  id="theme-toggle"
  aria-label="Toggle Theme"
  class="group flex h-8 w-8 items-center justify-center rounded-sm border border-black/15 bg-neutral-100 p-1.5 text-xs transition-colors duration-0 hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:bg-neutral-900 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white theme-toggle"
>
  <svg
    id="sun-icon"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    class="hidden h-6 w-6 md:h-5 md:w-5 dark:block transition-transform duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:rotate-12 group-active:rotate-180"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <circle cx="12" cy="12" r="5"></circle>
    <line x1="12" y1="1" x2="12" y2="3"></line>
    <line x1="12" y1="21" x2="12" y2="23"></line>
    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
    <line x1="1" y1="12" x2="3" y2="12"></line>
    <line x1="21" y1="12" x2="23" y2="12"></line>
    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
  </svg>
  <svg
    id="moon-icon"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    class="block h-6 w-6 md:h-5 md:w-5 dark:hidden transition-transform duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:rotate-12 group-active:rotate-180"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
  </svg>
</button>

<script>
  // Use a single RAF call to batch DOM updates
  let rafId: number | null = null;

  // Immediately invoked function to ensure proper scoping
  (function () {
    // Check if we're in a browser environment
    if (typeof document === "undefined") return;

    // Get all theme toggle buttons once
    const themeToggles = document.querySelectorAll("#theme-toggle");
    if (!themeToggles.length) return;

    // Debounce function to prevent rapid theme toggles
    function debounce<T extends any[]>(
      func: (...args: T) => void,
      wait: number,
    ) {
      let timeout: ReturnType<typeof setTimeout> | null = null;
      return function executedFunction(...args: T) {
        const later = () => {
          timeout = null;
          func(...args);
        };
        if (timeout !== null) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(later, wait);
      };
    }

    // Function to get the current theme
    const getTheme = (): "dark" | "light" => {
      try {
        // Check for saved theme preference first
        if (typeof localStorage !== "undefined" && localStorage.theme) {
          return localStorage.theme;
        }
        // Fall back to system preference
        return window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light";
      } catch (e) {
        return "light"; // Default to light if there's an error
      }
    };

    // Batch DOM updates using requestAnimationFrame
    const batchThemeUpdates = (theme: string) => {
      if (rafId) cancelAnimationFrame(rafId);

      rafId = requestAnimationFrame(() => {
        try {
          // Batch DOM updates
          const updates = [];

          // Update theme class
          updates.push(() => {
            if (theme === "dark") {
              document.documentElement.classList.add("dark");
              document.documentElement.style.colorScheme = "dark";
            } else {
              document.documentElement.classList.remove("dark");
              document.documentElement.style.colorScheme = "light";
            }
            document.documentElement.setAttribute("data-theme", theme);
          });

          // Execute all updates in a single frame
          updates.forEach((update) => update());

          // Don't save theme to localStorage here, only save on explicit user action
        } catch (e) {
          console.error("Error applying theme:", e);
        } finally {
          rafId = null;
        }
      });
    };

    // Toggle between light and dark theme
    const toggleTheme = debounce(() => {
      const currentTheme = getTheme();
      const newTheme = currentTheme === "dark" ? "light" : "dark";

      // Only save to localStorage when user explicitly toggles
      try {
        localStorage.setItem("theme", newTheme);
      } catch (e) {
        console.warn("Could not save theme preference:", e);
      }

      batchThemeUpdates(newTheme);
    }, 100); // 100ms debounce time

    // Initialize theme on page load
    const initTheme = () => {
      const theme = getTheme();
      batchThemeUpdates(theme);
    };

    // Handle Astro view transitions
    const handlePageTransition = () => {
      const theme = getTheme();
      batchThemeUpdates(theme);
    };

    // Watch for system theme changes (only if no explicit preference is set)
    const setupSystemThemeListener = () => {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleSystemThemeChange = (e: { matches: any }) => {
        // Only update if user hasn't set a preference
        if (!localStorage.theme) {
          batchThemeUpdates(e.matches ? "dark" : "light");
        }
      };

      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener("change", handleSystemThemeChange);
      } else if (mediaQuery.addListener) {
        mediaQuery.addListener(handleSystemThemeChange);
      }

      return () => {
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener("change", handleSystemThemeChange);
        } else if (mediaQuery.removeListener) {
          mediaQuery.removeListener(handleSystemThemeChange);
        }
      };
    };

    // Initialize everything
    const init = () => {
      initTheme();
      const cleanupSystemTheme = setupSystemThemeListener();

      // Add click event listeners to all theme toggle buttons
      const attachEventListeners = () => {
        const currentToggles = document.querySelectorAll("#theme-toggle");
        currentToggles.forEach((toggle) => {
          // Remove any existing listeners to prevent duplicates
          const newToggle = toggle.cloneNode(true);
          toggle.parentNode?.replaceChild(newToggle, toggle);
          
          newToggle.addEventListener("click", (e) => {
            e.preventDefault();
            toggleTheme();
          });
        });
      };
      
      // Initial attachment
      attachEventListeners();

      // Handle Astro view transitions
      const handleViewTransition = () => {
        handlePageTransition();
        // Reattach event listeners after DOM is updated
        setTimeout(attachEventListeners, 0);
      };
      
      document.addEventListener("astro:after-swap", handleViewTransition);

      // Cleanup function
      return () => {
        cleanupSystemTheme();
        document.removeEventListener("astro:after-swap", handleViewTransition);
        if (rafId) {
          cancelAnimationFrame(rafId);
          rafId = null;
        }
      };
    };

    // Start initialization
    const cleanup = init();

    // Cleanup on component unmount
    return () => {
      if (cleanup) cleanup();
    };
  })();
</script>
