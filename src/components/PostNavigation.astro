---
const { prevPost, nextPost, path } = Astro.props;
---

<div class="flex flex-col gap-3 md:grid md:grid-cols-2 md:gap-1.5 lg:gap-3">
  {
    prevPost?.id && (
      <a
        href={`/${path}/${prevPost?.id.replace(/\/index\.mdx?$/, '')}`}
        class="group relative flex w-full flex-nowrap rounded-lg border border-black/15 px-4 py-3 pl-10 no-underline transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          class="absolute left-2 top-1/2 size-5 -translate-y-1/2 fill-none stroke-current stroke-2"
        >
          <line
            x1="5"
            y1="12"
            x2="19"
            y2="12"
            class="translate-x-3 scale-x-0 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-hover:scale-x-100 group-focus-visible:translate-x-0 group-focus-visible:scale-x-100"
          />
          <polyline
            points="12 5 5 12 12 19"
            class="translate-x-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-focus-visible:translate-x-0"
          />
        </svg>
        <div class="flex items-center text-sm">{prevPost?.data.title}</div>
      </a>
    )
  }

  {
    nextPost?.id && (
      <a
        href={`/${path}/${nextPost?.id.replace(/\/index\.mdx?$/, '')}`}
        class="group relative flex w-full grow flex-row-reverse flex-nowrap rounded-lg border border-black/15 px-4 py-3 pr-10 no-underline transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white md:py-4"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          class="absolute right-2 top-1/2 size-5 -translate-y-1/2 fill-none stroke-current stroke-2"
        >
          <line
            x1="5"
            y1="12"
            x2="19"
            y2="12"
            class="translate-x-3 scale-x-0 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-hover:scale-x-100 group-focus-visible:translate-x-0 group-focus-visible:scale-x-100"
          />
          <polyline
            points="12 5 19 12 12 19"
            class="-translate-x-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-focus-visible:translate-x-0"
          />
        </svg>
        <div class="flex items-center text-sm">{nextPost?.data.title}</div>
      </a>
    )
  }
</div>
