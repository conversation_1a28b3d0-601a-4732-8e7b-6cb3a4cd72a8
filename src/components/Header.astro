---
import Container from "@components/Container.astro";
import Link from "@components/Link.astro";
import ThemeToggle from "@components/ThemeToggle.astro";
import { SITE } from "@consts";
---

<header transition:persist>
  <Container>
    <div class="flex flex-wrap justify-between gap-y-2">
      <Link href="/" underline={false}>
        <div class="pt-1 text-lg font-semibold md:text-xl">
          {SITE.TITLE}&nbsp;🕹️
        </div>
      </Link>

      <!-- Desktop Navigation -->
      <nav class="hidden items-center gap-1 text-sm md:flex">
        <Link href="/blog">blog</Link>
        <span>
          {`/`}
        </span>
        <Link href="/projects">projects</Link>
        <span>
          {`/`}
        </span>
        <Link href="/notes">notes</Link>
        <span>
          {`/`}
        </span>
        <Link href="/snippets">snippets</Link>
        <span>
          {`/`}
        </span>

        <ThemeToggle />
        <button
          id="magnifying-glass"
          aria-label="Search"
          class="flex h-8 items-center gap-1 rounded-sm border border-black/15 bg-neutral-100 px-2 text-xs transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:bg-neutral-900 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
        >
          <span class="sr-only">Search</span>
          <svg
            width="16"
            height="16"
            viewBox="0 0 15 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
          >
            <path
              d="M10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5ZM9.30884 10.0159C8.53901 10.6318 7.56251 11 6.5 11C4.01472 11 2 8.98528 2 6.5C2 4.01472 4.01472 2 6.5 2C8.98528 2 11 4.01472 11 6.5C11 7.56251 10.6318 8.53901 10.0159 9.30884L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L9.30884 10.0159Z"
              fill="currentColor"
              fill-rule="evenodd"
              clip-rule="evenodd"></path>
          </svg>
          &nbsp;Search
        </button>
      </nav>

      <!-- Mobile Navigation -->
      <div class="flex items-center gap-2 md:hidden">
        <ThemeToggle />
        <button
          id="mobile-search-button"
          aria-label="Search"
          class="flex h-8 w-8 items-center justify-center rounded-sm border border-black/15 bg-neutral-100 p-1.5 text-xs transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:bg-neutral-900 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
        >
          <svg
            height="24"
            stroke-linejoin="round"
            viewBox="0 0 16 16"
            width="24"
            style="color: currentcolor;"
            ><path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.5 7C3.5 5.067 5.067 3.5 7 3.5C8.933 3.5 10.5 5.067 10.5 7C10.5 7.88461 10.1718 8.69256 9.63058 9.30876L9.30876 9.63058C8.69256 10.1718 7.88461 10.5 7 10.5C5.067 10.5 3.5 8.933 3.5 7ZM9.96544 11.0261C9.13578 11.6382 8.11014 12 7 12C4.23858 12 2 9.76142 2 7C2 4.23858 4.23858 2 7 2C9.76142 2 12 4.23858 12 7C12 8.11014 11.6382 9.13578 11.0261 9.96544L14.0303 12.9697L14.5607 13.5L13.5 14.5607L12.9697 14.0303L9.96544 11.0261Z"
              fill="currentColor"></path></svg
          >
        </button>
        <button
          id="mobile-menu-button"
          aria-label="Toggle Menu"
          class="flex items-center rounded-sm border border-black/15 bg-neutral-100 px-2 py-1 text-xs transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:bg-neutral-900 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </button>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="fixed inset-0 z-50 hidden bg-[var(--color-background)] pt-20 pb-10 opacity-0 shadow-lg backdrop-blur-sm transition-all duration-300 ease-in-out dark:bg-[var(--color-background)]"
        >
          <button
            id="mobile-menu-close"
            aria-label="Close Menu"
            class="absolute top-[1.125rem] right-6 flex items-center rounded-sm border border-black/15 bg-neutral-100 px-2 py-1 text-xs transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:bg-neutral-900 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <nav
            class="flex translate-y-[-100%] transform flex-col items-center gap-6 rounded-b-3xl bg-[var(--color-background)] pb-10 text-lg shadow-md transition-all duration-300 ease-in-out dark:bg-[var(--color-background)]"
            id="mobile-nav"
          >
            <Link href="/blog">blog</Link>
            <Link href="/projects">projects</Link>
            <Link href="/notes">notes</Link>
            <Link href="/snippets">snippets</Link>
          </nav>
        </div>
      </div>
    </div>
  </Container>
</header>

<script>
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const mobileMenu = document.getElementById("mobile-menu");
  const mobileMenuClose = document.getElementById("mobile-menu-close");
  const mobileSearchButton = document.getElementById("mobile-search-button");
  const magnifyingGlass = document.getElementById("magnifying-glass");
  const mobileNav = document.getElementById("mobile-nav");
  const mobileNavLinks = mobileNav?.querySelectorAll("a");
  const mainContent = document.querySelector("main");

  function toggleMenu() {
    if (mobileMenu?.classList.contains("hidden")) {
      // Opening the menu
      mobileMenu.classList.remove("hidden");
      // Force a reflow
      mobileMenu.offsetHeight;
      mobileMenu.classList.remove("opacity-0");
      mobileNav?.classList.remove("translate-y-[-100%]");
      mainContent?.classList.add(
        "blur-[2px]",
        "transition-all",
        "duration-300",
      );
    } else {
      // Closing the menu
      mobileMenu?.classList.add("opacity-0");
      mobileNav?.classList.add("translate-y-[-100%]");
      mainContent?.classList.remove("blur-[2px]");
      // Wait for animation to complete before hiding
      setTimeout(() => {
        mobileMenu?.classList.add("hidden");
      }, 300);
    }
    document.body.classList.toggle("overflow-hidden");
  }

  function closeMenu() {
    mobileMenu?.classList.add("opacity-0");
    mobileNav?.classList.add("translate-y-[-100%]");
    mainContent?.classList.remove("blur-[2px]");
    setTimeout(() => {
      mobileMenu?.classList.add("hidden");
      document.body.classList.remove("overflow-hidden");
    }, 300);
  }

  mobileMenuButton?.addEventListener("click", toggleMenu);
  mobileMenuClose?.addEventListener("click", toggleMenu);

  // Close menu when clicking on navigation links
  mobileNavLinks?.forEach((link) => {
    link.addEventListener("click", closeMenu);
  });

  // Ensure search functionality works on mobile
  mobileSearchButton?.addEventListener("click", () => {
    magnifyingGlass?.click();
  });
</script>
