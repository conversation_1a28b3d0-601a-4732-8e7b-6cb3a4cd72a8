---
interface Entry {
  id: string;
  slug: string;
  collection: string;
  data: {
    title: string;
    description: string;
    date: Date;
    draft?: boolean;
    tags?: string[];
    redirected?: boolean;
    redirectedUrl?: string;
  };
}

const { entry } = Astro.props as { entry: Entry };

// Generate the URL based on the collection and ID
const getUrl = (entry: Entry) => {
  if (entry.data.redirected && entry.data.redirectedUrl) {
    return entry.data.redirectedUrl;
  }
  // Remove /index.mdx or /index.md from blog post IDs
  const cleanId = entry.collection === 'blog' 
    ? entry.id.replace(/\/index\.mdx?$/, '') 
    : entry.id;
  return `/${entry.collection}/${cleanId}`;
};

const url = getUrl(entry);
const isExternal = url.startsWith('http');
const linkProps = isExternal 
  ? { 
      target: '_blank', 
      rel: 'noopener noreferrer',
      href: url 
    } 
  : { href: url };
---

<a
  {...linkProps}
  class="not-prose group relative flex flex-nowrap rounded-lg border border-black/15 px-4 py-3 pr-10 transition-colors duration-300 ease-in-out hover:bg-black/5 hover:text-black focus-visible:bg-black/5 focus-visible:text-black dark:border-white/20 dark:hover:bg-white/5 dark:hover:text-white dark:focus-visible:bg-white/5 dark:focus-visible:text-white"
>
  <div class="flex flex-1 flex-col truncate">
    <div class="font-semibold">
      {entry.data.title}
    </div>
    <div class="text-sm">
      {entry.data.description}
    </div>
  </div>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    class="absolute right-2 top-1/2 size-5 -translate-y-1/2 fill-none stroke-current stroke-2"
  >
    {isExternal ? (
      <path
        d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6m4-3h6v6m-11 5L21 3"
        class="transition-transform duration-300 ease-in-out group-hover:translate-x-0.5 group-focus-visible:translate-x-0.5"
      />
    ) : (
      <>
        <line
          x1="5"
          y1="12"
          x2="19"
          y2="12"
          class="translate-x-3 scale-x-0 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-hover:scale-x-100 group-focus-visible:translate-x-0 group-focus-visible:scale-x-100"
        />
        <polyline
          points="12 5 19 12 12 19"
          class="-translate-x-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0 group-focus-visible:translate-x-0"
        />
      </>
    )}
  </svg>
</a>
