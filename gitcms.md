---
collections:
  - file_extension: .mdx
    filename_pattern: ''
    git_path: /src/content/blog
    metadata_schema:
      - name: title
        required: true
        type: title
      - name: description
        required: true
        type: singleline
      - name: date
        required: true
        type: datetime
      - name: draft
        required: true
        type: boolean
      - name: tags
        required: true
        type: multi_select
        allowed_values:
          - git
          - oss
    name: Blogs
  - file_extension: .mdx
    filename_pattern: ''
    git_path: /src/content/projects
    metadata_schema:
      - name: title
        required: true
        type: title
      - name: description
        required: true
        type: singleline
      - name: date
        required: true
        type: datetime
      - name: draft
        required: true
        type: boolean
      - name: demoURL
        required: false
        type: singleline
      - name: repoURL
        required: false
        type: multi_select
        allowed_values: []
    name: Projects
  - name: Notes
    git_path: /src/content
    filename_pattern: ''
    file_extension: .mdx
    metadata_schema:
      - name: title
        type: title
        required: true
      - name: description
        type: singleline
        required: true
      - name: date
        type: datetime
        required: true
      - name: draft
        type: boolean
        required: true
      - name: tags
        type: multi_select
        required: true
        allowed_values: []
media_path: /public
repo: d-cryptic/portfolio
website_url: https://barundebnath.com
---
# GitCMS Configuration File
> [!WARNING]
> This is autogenerated configutation file by [GitCMS](https://gitcms.blog). Don't edit it on your own, edit it **only** using GitCMS UI
